ART-ORBO Project Portability Changes
=====================================

This document summarizes all the changes made to make the ART-ORBO project portable 
across different machines and environments by converting absolute file paths to relative paths.

CHANGES MADE:
=============

1. Eclipse User Libraries Configuration (ART.userlibraries)
   - Changed all absolute paths from "C:/Users/<USER>/IdeaProjects/ART/libs/..." 
     to relative paths "libs/..."
   - This allows the project to find JAR dependencies relative to the project root
   - Affected libraries: gral-core, jacocoagent, jheatchart, jmathplot, junit, 
     log4j-api, log4j-core, jacoco.core, jacoco.report, poi, and 3d/jmathplot

2. Java Source Files with Hardcoded Paths
   - MainMethodEm.java: Changed output path from absolute Windows path to "outputs/em/simulation"
   - MainMethodTime.java: Changed output path to "outputs/time/0-1000000"
   - MainMethodRealityPm.java: Changed output path to "outputs/Pm1"
   - MainMethodRealityEm.java: Changed output path to "outputs/Em2"
   - MainMethodRealityBoxplot.java: Changed output path to "outputs/boxplot/"
   - MainMethodBoxPlot.java: Changed output path to "outputs/boxplot/2维0.005"
   - MainMethodBoxPlot2.java: Changed output path to "outputs/boxplot/"+d+"维"+failrates[0]

3. Eclipse Classpath Configuration (.classpath)
   - Removed absolute path reference "/ART_Heuristic/libs/poi-3.16.jar"
   - Removed reference to external project "/ART_ORB_Em"
   - Changed to relative path "libs/poi-3.16.jar"

4. Coverage Report Generator (src/coverage/report/ReportGenerator.java)
   - Changed hardcoded absolute path "C:\\Users\\<USER>\\EclipseProjects\\ART"
     to relative path "." (current directory)

5. Readme Files (src/tested/readme.txt and bin/tested/readme.txt)
   - Updated workspace location reference from "${workspace_loc}/ART/Resource"
     to "${workspace_loc}/ART-ORBO/Resource"

BENEFITS:
=========

After these changes, the project is now portable and can be:
- Copied to any directory on any machine
- Shared with other developers without path conflicts
- Run from different locations without modification
- Used in different development environments (Eclipse, IntelliJ, VS Code, etc.)

REQUIREMENTS FOR RUNNING:
========================

1. Ensure all JAR files are present in the "libs/" directory
2. Ensure native libraries (.dll files) are present in the "Resource/" directory
3. The "outputs/" directory will be created automatically when running the main methods
4. For Eclipse users: Import the project and the relative paths will work automatically
5. For command line usage: Run from the project root directory

NOTES:
======

- All output files will now be generated in the "outputs/" directory relative to the project root
- The project structure remains unchanged, only path references were made relative
- Maven dependencies are handled through pom.xml and don't require path changes
- Native library paths in readme.txt files use Eclipse workspace variables for portability

Date: 2025-06-21
Changes made by: Augment Agent
