<?xml version="1.0" encoding="UTF-8"?>
<eclipse-userlibraries>
  <library name="3d">
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/gral-core-0.11.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/jacocoagent.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/jheatchart-0.6.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/jmathplot.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/junit-4.12.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/log4j-api-2.5.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/log4j-core-2.5.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/org.jacoco.core-0.7.6.201602180812.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/org.jacoco.report-0.7.6.201602180812.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/poi-3.16.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/hamcrest-core-1.3.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/org.jacoco.core-0.7.6.201602180812.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/org.jacoco.report-0.7.6.201602180812.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/jacocoagent.jar" />
    <archive path="C:/Users/<USER>/Desktop/java workspace/ART-ORBO/libs/3d/jmathplot.jar" />
  </library>
  <library name="Maven: junit:junit:4.12">
    <archive path="C:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar" />
  </library>
  <library name="Maven: org.hamcrest:hamcrest-core:1.3">
    <archive path="C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar" />
  </library>
  <library name="Maven: org.jacoco:org.jacoco.agent:0.7.6.201602180812">
    <archive path="C:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.7.6.201602180812/org.jacoco.agent-0.7.6.201602180812.jar" />
  </library>
  <library name="Maven: org.ow2.asm:asm-all:5.0.4">
    <archive path="C:/Users/<USER>/.m2/repository/org/ow2/asm/asm-all/5.0.4/asm-all-5.0.4.jar" />
  </library>
</eclipse-userlibraries>

